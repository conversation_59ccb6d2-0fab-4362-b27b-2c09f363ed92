import 'package:flutter_test/flutter_test.dart';
import 'package:quycky/core/services/storage/storage_sharedpreferences_client_implementation.dart';
import 'package:quycky/core/services/system_badge_service/system_badge_service.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  group('Storage Tests', () {
    setUp(() {
      // Mock SharedPreferences for testing
      SharedPreferences.setMockInitialValues({});
    });

    test('StorageSharedPreferencesClientImplementation should read and write correctly', () async {
      final storage = StorageSharedPreferencesClientImplementation();
      
      // Test write
      final writeResult = await storage.write('test_key', 'test_value');
      expect(writeResult, true);
      
      // Test read
      final readResult = await storage.read('test_key');
      expect(readResult, 'test_value');
    });

    test('SystemBadgeService should handle storage operations correctly', () async {
      // Test getCurrentSystemBadgeCurrentCount with empty storage
      final initialCount = await SystemBadgeService.getCurrentSystemBadgeCurrentCount();
      expect(initialCount, 0);
      
      // Test updateSystemBadgeCount
      await SystemBadgeService.updateSystemBadgeCount(5);
      
      // Verify the count was saved
      final updatedCount = await SystemBadgeService.getCurrentSystemBadgeCurrentCount();
      expect(updatedCount, 5);
    });

    test('Storage should handle errors gracefully', () async {
      final storage = StorageSharedPreferencesClientImplementation();
      
      // Test reading non-existent key
      final result = await storage.read('non_existent_key');
      expect(result, '');
    });
  });
}
