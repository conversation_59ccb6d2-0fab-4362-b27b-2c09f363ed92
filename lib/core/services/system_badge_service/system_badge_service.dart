import 'package:flutter/material.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:quycky/core/services/storage/storage_sharedpreferences_client_implementation.dart';
import 'package:quycky/core/utils/app_storage_keys.dart';
import 'package:quycky/core/utils/type_converters.dart';

class SystemBadgeService {
  static Future<int> getCurrentSystemBadgeCurrentCount() async {
    try {
      final storage = StorageSharedPreferencesClientImplementation();
      final storedValue = await storage.read(AppStorageKeys.systemBadgeCount);
      int currentBadgeCount = dynamicToInt(storedValue);
      return currentBadgeCount;
    } catch (e) {
      debugPrint('🔔 Error reading badge count from storage: $e');
      return 0; // Retorna 0 em caso de erro
    }
  }

  static Future<void> updateSystemBadgeCount(int count) async {
    try {
      final storage = StorageSharedPreferencesClientImplementation();
      await storage.write(AppStorageKeys.systemBadgeCount, count.toString());
      debugPrint('🔔 Badge count saved to storage: $count');
    } catch (e) {
      debugPrint('🔔 Error saving badge count to storage: $e');
    }
  }

  static Future<void> updateBadgeCount(int count) async {
    try {
      bool isSupported = await FlutterAppBadger.isAppBadgeSupported();

      if (isSupported) {
        if (count > 0) {
          final finalCount = await getCurrentSystemBadgeCurrentCount() + count;
          debugPrint(
              'Glória a Deus!!!\\o/==> 🔔 Badge count updated to $finalCount');
          FlutterAppBadger.updateBadgeCount(finalCount);
          await updateSystemBadgeCount(finalCount);
        } else {
          await removeBadge();
        }
      }
    } catch (e) {
      debugPrint('🔔 Error updating badge count: $e');
    }
  }

  static Future<void> removeBadge() async {
    try {
      bool isSupported = await FlutterAppBadger.isAppBadgeSupported();

      if (isSupported) {
        FlutterAppBadger.removeBadge();
        await updateSystemBadgeCount(0);
        debugPrint('🔔 Badge removed successfully');
      }
    } catch (e) {
      debugPrint('🔔 Error removing badge: $e');
    }
  }
}
