import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:quycky/core/services/push_service/abstract_push_service.dart';
import 'package:quycky/core/services/push_service/model/notification_entity.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:quycky/core/services/push_service/push_service.dart';
import 'package:quycky/core/services/push_service/notification_test_helper.dart';

class PushServiceFCMImplementation extends PushService
    implements AbstractPushService {
  PushServiceFCMImplementation(
    super.gameStorage,
    super.userStorage,
    super.acceptInviteUseCase,
    super.openToInvitedGameRoomUseCaseUseCase,
    super.userController,
  ) {
    startPushService();
  }

  void startPushService() {
    getPermissions();
    setListeners();
    getToken();
    startTokenRefreshListener(); // Ativa o listener de refresh do token
    verifyForInitialMessage();
    startNotificationListeners();

    // Log notification capabilities for debugging
    NotificationTestHelper.logNotificationCapabilities();
  }

  void updateToken(String newToken) {
    debugPrint('🔔 FCM Token updated: ${newToken.substring(0, 20)}...');
    pushServiceKey = newToken;
  }

  void startTokenRefreshListener() {
    FirebaseMessaging.instance.onTokenRefresh.listen((event) {
      debugPrint('🔔 FCM Token refresh detected');
      updateToken(event);
    }, onError: (error) {
      debugPrint('🔔 Error in token refresh listener: $error');
    });
  }

  verifyForInitialMessage() async {
    final event = await FirebaseMessaging.instance.getInitialMessage();
    final receivedEvent = FCMBackgroundMessageHandle.remoteMessage ??
        (event != null && event.data.containsKey('type') ? event : null);
    if (receivedEvent != null) {
      onNotificationEvent(receivedEvent, onReceiveNotificationForeground,
          openingApp: true);
    }
    FCMBackgroundMessageHandle.remoteMessage = null;
  }

  setListeners() async {
    // O background handler já está registrado no main.dart
    // Removendo a duplicação que causava o problema dos badges duplicados
    // FirebaseMessaging.onBackgroundMessage(
    //     main.firebaseMessagingBackgroundHandler);

    FirebaseMessaging.onMessageOpenedApp.listen((event) {
      NotificationTestHelper.logIncomingMessage(event, 'APP_OPENED');
      onNotificationEvent(event, onReceiveNotificationForeground,
          openingApp: true);
    });

    FirebaseMessaging.onMessage.listen((event) {
      NotificationTestHelper.logIncomingMessage(event, 'FOREGROUND');
      onNotificationEvent(event, onReceiveNotificationForeground);
    });
  }

  onNotificationEvent(
      RemoteMessage event,
      void Function(String type, String? message, Map<String, dynamic> data,
              {bool openingApp})
          func,
      {openingApp = false}) {
    String type = event.data.containsKey('type') ? event.data['type'] : '_';
    String? message = event.notification?.body;
    Map<String, dynamic> data =
        event.data.containsKey('data') ? json.decode(event.data['data']) : {};
    func(type, message, data, openingApp: openingApp);
  }

  getPermissions() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    await messaging.setForegroundNotificationPresentationOptions(
      alert: false, // Não mostra o banner/alerta em primeiro plano.
      badge: true, // Permite atualizar o badge do app.
      sound: true, // Permite tocar o som.
    );

    await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );
  }

  @override
  Future<void> deleteToken() async {
    return FirebaseMessaging.instance.deleteToken();
  }

  @override
  Future<String> getToken() async {
    try {
      String? res = await FirebaseMessaging.instance.getToken();
      if (res != null && res.isNotEmpty) {
        updateToken(res);
        debugPrint('🔔 FCM Token obtained successfully');
      } else {
        debugPrint('🔔 Warning: FCM Token is null or empty');
      }
      return pushServiceKey;
    } catch (e) {
      debugPrint('🔔 Error getting FCM token: $e');
      return pushServiceKey;
    }
  }

  onTapBackgroundNotification() {}
}

class FCMBackgroundMessageHandle {
  static NotificationEntity? notificationEntity;
  static RemoteMessage? remoteMessage;
}
