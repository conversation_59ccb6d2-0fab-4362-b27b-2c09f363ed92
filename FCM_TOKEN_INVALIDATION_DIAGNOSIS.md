# Diagnóstico: Token FCM Invalidado Após Algumas Notificações

## 🔍 Problema Identificado
O token FCM funciona inicialmente mas se torna inválido após algumas notificações, retornando erro HTTP 400 "BadDeviceToken".

## 🚨 Principais Causas Possíveis

### 1. **APNS Token Changes (Mais Prov<PERSON>vel)**
- iOS pode renovar o APNS token automaticamente
- FCM detecta mudança e invalida o token atual
- Acontece especialmente quando:
  - App vai para background/foreground
  - Dispositivo perde/recupera conectividade
  - Sistema iOS otimiza recursos

### 2. **Ambiente Mismatch**
- Token gerado em sandbox mas servidor usa production
- Certificados APNS incorretos ou expirados
- Configuração Firebase inconsistente

### 3. **Conectividade de Rede**
- Perda temporária de conexão
- Reconexão gera novo APNS token
- Token FCM fica desatualizado

## ✅ Melhorias Implementadas

### 1. **Token Refresh Listener Ativado**
```dart
// Agora ativo no startPushService()
startTokenRefreshListener();
```

### 2. **Logs Detalhados Adicionados**
```dart
// Logs para monitorar mudanças de token
debugPrint('🔔 FCM Token updated: ${newToken.substring(0, 20)}...');
debugPrint('🔔 FCM Token refresh detected');
debugPrint('🔔 Push token changed, updating server...');
```

### 3. **Tratamento de Erro Melhorado**
```dart
// Error handling no token refresh
onError: (error) {
  debugPrint('🔔 Error in token refresh listener: $error');
}
```

## 🔧 Próximos Passos para Diagnóstico

### 1. **Monitorar Logs**
Execute o app e observe os logs para:
- `🔔 FCM Token refresh detected` - indica quando token muda
- `🔔 Push token changed, updating server...` - confirma atualização no servidor
- `🔔 Error in token refresh listener` - identifica problemas

### 2. **Testar Cenários Específicos**
- Envie notificação → funciona
- Coloque app em background por 5+ minutos
- Volte app para foreground
- Envie nova notificação → verifique se ainda funciona

### 3. **Verificar Configuração iOS**

#### A) Certificados APNS
```bash
# Verificar se certificados estão corretos no Firebase Console
# Development vs Production environment
```

#### B) Bundle ID e Team ID
```bash
# Confirmar se Bundle ID no Xcode == Firebase Console
# Verificar Team ID está correto
```

### 4. **Implementar Retry Logic**
Se token for invalidado, implementar lógica para:
- Detectar erro 400 BadDeviceToken
- Forçar refresh do token
- Reenviar para servidor

## 📱 Comandos de Teste

### 1. **Verificar Token Atual**
```dart
final token = await FirebaseMessaging.instance.getToken();
print('Current token: $token');
```

### 2. **Forçar Refresh Manual**
```dart
await FirebaseMessaging.instance.deleteToken();
final newToken = await FirebaseMessaging.instance.getToken();
```

### 3. **Monitorar Estado do App**
```dart
// Adicionar listener para mudanças de estado
WidgetsBinding.instance.addObserver(this);
```

## 🎯 Solução Recomendada

### Implementar Token Validation Service
```dart
class FCMTokenValidator {
  static Future<bool> validateToken(String token) async {
    // Enviar notificação de teste
    // Se retornar 400, token é inválido
    // Forçar refresh automático
  }
}
```

## 📊 Métricas para Monitorar

1. **Frequência de Token Refresh**
   - Quantas vezes por dia o token muda
   - Correlação com uso do app

2. **Taxa de Falha de Notificação**
   - % de notificações que falham
   - Tempo entre falhas

3. **Padrões de Invalidação**
   - Horários específicos
   - Após ações específicas do usuário

## 🔄 Próxima Iteração

Após implementar os logs, colete dados por 24-48h e analise:
- Quando exatamente o token é invalidado
- Se há padrão temporal
- Correlação com ações do usuário
- Frequência de refresh automático

Com esses dados, poderemos implementar uma solução mais específica.
